# 服务时间问题修复说明

## 问题描述

用户反馈："我没有先期望上门时间，但我看创建订单时serviceTime却有一个时间"

## 问题分析

### 1. 问题现象
- 用户没有主动选择期望上门时间
- 但在创建订单时，`serviceTime` 字段却有值
- 这导致订单包含了用户未明确选择的服务时间

### 2. 可能的原因

#### 原因1：本地存储残留数据
- 用户之前可能在其他订单中选择过期望服务时间
- `expectServiceTime` 被保存在 `wx.getStorageSync('expectServiceTime')` 中
- 当用户重新进入服务预约页面时，之前的时间数据仍然存在
- 在 `buildOrderInfo()` 方法中，如果检测到 `expectServiceTime` 有值，就会设置 `orderInfo.serviceTime`

#### 原因2：时间选择器默认值问题
- `custom-picker` 组件在初始化时设置了默认的时间选择器值
- 虽然组件本身不会自动调用 `clickEvent`，但可能存在其他触发机制

### 3. 代码分析

#### 关键代码位置

**服务预约页面 - buildOrderInfo() 方法**
```javascript
// pages/service/reservation/index.js 第804行
const expectTime = wx.getStorageSync('expectServiceTime');

// 第878-884行
if (expectTime) {
  console.log('设置订单服务时间:', expectTime);
  console.log('转换后的时间对象:', new Date(expectTime));
  orderInfo.serviceTime = new Date(expectTime);
} else {
  console.log('没有设置期望服务时间，订单将不包含 serviceTime');
}
```

**时间选择事件**
```javascript
// pages/service/reservation/index.js 第35-39行
clickTimeEvent: date => {
  console.log('=== 用户选择了期望服务时间 ===', date);
  wx.setStorageSync('expectServiceTime', date);
  console.log('已保存期望服务时间到本地存储');
},
```

## 解决方案

### 1. 智能清除历史数据
实现智能清除逻辑，只有当前页面正在处理的服务ID与缓存不匹配时才清除：

```javascript
/**
 * 智能清除期望服务时间
 * 只有当前页面正在处理的服务ID与缓存不匹配时才清除
 * 通过单流程初次进入页面时服务ID是不存在的，这时可以无条件清除
 */
smartClearExpectServiceTime(option) {
  const currentServiceId = option.serviceId;
  const expectServiceTime = wx.getStorageSync('expectServiceTime');
  const cachedServiceId = wx.getStorageSync('expectServiceTime_serviceId');

  // 判断是否需要清除期望服务时间
  let shouldClear = false;
  let clearReason = '';

  if (!expectServiceTime) {
    // 如果没有期望服务时间，无需清除
    return;
  }

  if (!cachedServiceId) {
    // 如果没有缓存的服务ID，说明是旧数据，需要清除
    shouldClear = true;
    clearReason = '没有缓存的服务ID，可能是旧数据';
  } else if (currentServiceId && cachedServiceId !== currentServiceId) {
    // 如果当前服务ID与缓存的服务ID不匹配，需要清除
    shouldClear = true;
    clearReason = '当前服务ID与缓存的服务ID不匹配';
  } else if (!currentServiceId) {
    // 如果当前没有服务ID（异常情况），为安全起见清除
    shouldClear = true;
    clearReason = '当前没有服务ID，为安全起见清除';
  }

  if (shouldClear) {
    wx.removeStorageSync('expectServiceTime');
    wx.removeStorageSync('expectServiceTime_serviceId');
  }
}
```

### 2. 关联服务ID存储
在用户选择时间时，同时保存当前服务ID用于后续判断：

```javascript
clickTimeEvent: date => {
  wx.setStorageSync('expectServiceTime', date);

  // 同时保存当前服务ID，用于后续智能清除判断
  const serviceInfo = wx.getStorageSync('selectServiceInfo');
  if (serviceInfo && serviceInfo.id) {
    wx.setStorageSync('expectServiceTime_serviceId', serviceInfo.id);
  }
},
```

### 3. 添加调试日志
为了更好地追踪问题，添加了详细的调试日志：

- 在智能清除逻辑中记录判断过程
- 在 `onShow` 中检查状态变化
- 在 `clickTimeEvent` 中记录用户选择和服务ID关联
- 在 `buildOrderInfo` 中记录最终状态

### 4. 验证修复效果

修复后的预期行为：
1. **智能保持用户选择**：如果用户在同一服务中选择了时间，重新进入页面时会保持选择
2. **智能清除跨服务数据**：如果用户切换到不同服务，会自动清除之前的时间选择
3. **安全的默认行为**：对于异常情况（如缺少服务ID），会安全地清除历史数据
4. **精确的订单创建**：只有在用户明确选择了时间的情况下，才会设置 `serviceTime`

## 测试建议

### 1. 测试场景1：全新用户
- 清除小程序数据
- 进入服务预约页面
- 不选择时间，直接创建订单
- 验证订单中不包含 `serviceTime`

### 2. 测试场景2：同一服务的时间保持
- 在服务A中选择期望服务时间
- 退出页面后重新进入同一服务A
- 验证之前选择的时间仍然保持
- 创建订单，验证包含正确的 `serviceTime`

### 3. 测试场景3：跨服务的时间清除
- 在服务A中选择期望服务时间
- 切换到服务B的预约页面
- 验证服务A的时间选择被清除
- 不选择时间，直接创建订单
- 验证订单中不包含 `serviceTime`

### 4. 测试场景4：正常选择时间
- 进入服务预约页面
- 主动选择期望服务时间
- 创建订单
- 验证订单中包含正确的 `serviceTime`

### 5. 测试场景5：异常数据处理
- 手动在存储中设置 `expectServiceTime` 但不设置 `expectServiceTime_serviceId`
- 进入任意服务预约页面
- 验证异常数据被清除
- 验证页面正常工作

## 相关文件

- `pages/service/reservation/index.js` - 主要修复文件
- `components/custom-picker/custom-picker.js` - 时间选择器组件
- `pages/service/reservation/index.wxml` - 页面模板

## 注意事项

1. **智能化用户体验**：新的解决方案既避免了意外的服务时间设置，又保持了用户在同一服务中的选择偏好
2. **服务隔离**：不同服务之间的时间选择相互独立，避免了跨服务的数据污染
3. **向后兼容**：对于旧版本遗留的数据（没有服务ID关联），会安全地清除
4. **调试支持**：详细的日志记录便于问题追踪和调试
5. **存储优化**：使用 `expectServiceTime_serviceId` 作为关联存储，便于后续扩展和维护

## 技术优势

1. **精确控制**：基于服务ID的精确匹配，避免误清除
2. **用户友好**：保持用户在同一服务流程中的选择状态
3. **安全可靠**：对异常情况有完善的处理机制
4. **易于维护**：清晰的逻辑结构和完整的日志记录
