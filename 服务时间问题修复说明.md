# 服务时间问题修复说明

## 问题描述

用户反馈："我没有先期望上门时间，但我看创建订单时serviceTime却有一个时间"

## 问题分析

### 1. 问题现象
- 用户没有主动选择期望上门时间
- 但在创建订单时，`serviceTime` 字段却有值
- 这导致订单包含了用户未明确选择的服务时间

### 2. 可能的原因

#### 原因1：本地存储残留数据
- 用户之前可能在其他订单中选择过期望服务时间
- `expectServiceTime` 被保存在 `wx.getStorageSync('expectServiceTime')` 中
- 当用户重新进入服务预约页面时，之前的时间数据仍然存在
- 在 `buildOrderInfo()` 方法中，如果检测到 `expectServiceTime` 有值，就会设置 `orderInfo.serviceTime`

#### 原因2：时间选择器默认值问题
- `custom-picker` 组件在初始化时设置了默认的时间选择器值
- 虽然组件本身不会自动调用 `clickEvent`，但可能存在其他触发机制

### 3. 代码分析

#### 关键代码位置

**服务预约页面 - buildOrderInfo() 方法**
```javascript
// pages/service/reservation/index.js 第804行
const expectTime = wx.getStorageSync('expectServiceTime');

// 第878-884行
if (expectTime) {
  console.log('设置订单服务时间:', expectTime);
  console.log('转换后的时间对象:', new Date(expectTime));
  orderInfo.serviceTime = new Date(expectTime);
} else {
  console.log('没有设置期望服务时间，订单将不包含 serviceTime');
}
```

**时间选择事件**
```javascript
// pages/service/reservation/index.js 第35-39行
clickTimeEvent: date => {
  console.log('=== 用户选择了期望服务时间 ===', date);
  wx.setStorageSync('expectServiceTime', date);
  console.log('已保存期望服务时间到本地存储');
},
```

## 解决方案

### 1. 清除历史数据
在页面加载时清除之前的期望服务时间数据，确保每次进入页面都是全新的状态：

```javascript
onLoad(option) {
  console.log('=== onLoad 开始执行 ===');
  // 检查页面加载时的期望服务时间状态
  const expectServiceTime = wx.getStorageSync('expectServiceTime');
  console.log('onLoad 中检查期望服务时间:', expectServiceTime);
  
  // 清除之前的期望服务时间，确保每次进入页面都是全新的状态
  wx.removeStorageSync('expectServiceTime');
  console.log('已清除之前的期望服务时间');
  
  // ... 其他初始化代码
}
```

### 2. 添加调试日志
为了更好地追踪问题，添加了详细的调试日志：

- 在 `onLoad` 中检查初始状态
- 在 `onShow` 中检查状态变化
- 在 `clickTimeEvent` 中记录用户选择
- 在 `buildOrderInfo` 中记录最终状态

### 3. 验证修复效果

修复后的预期行为：
1. 用户进入服务预约页面时，`expectServiceTime` 为空
2. 只有当用户主动选择时间时，才会设置 `expectServiceTime`
3. 创建订单时，只有在用户明确选择了时间的情况下，才会设置 `serviceTime`

## 测试建议

### 1. 测试场景1：全新用户
- 清除小程序数据
- 进入服务预约页面
- 不选择时间，直接创建订单
- 验证订单中不包含 `serviceTime`

### 2. 测试场景2：有历史数据的用户
- 先创建一个包含时间的订单
- 重新进入服务预约页面
- 不选择时间，直接创建订单
- 验证订单中不包含 `serviceTime`

### 3. 测试场景3：正常选择时间
- 进入服务预约页面
- 主动选择期望服务时间
- 创建订单
- 验证订单中包含正确的 `serviceTime`

## 相关文件

- `pages/service/reservation/index.js` - 主要修复文件
- `components/custom-picker/custom-picker.js` - 时间选择器组件
- `pages/service/reservation/index.wxml` - 页面模板

## 注意事项

1. 这个修复确保了用户体验的一致性，避免了意外的服务时间设置
2. 调试日志可以在生产环境中移除，目前保留用于问题追踪
3. 如果后续需要保留用户的时间选择偏好，可以考虑使用不同的存储键名或添加时间戳验证
